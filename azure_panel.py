#!/usr/bin/env python3
"""
Azure Control Panel for Ubuntu Terminal GUI
Provides Azure CLI command buttons and configuration options
"""

import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                             QLabel, QLineEdit, QComboBox, QGroupBox, QFormLayout,
                             QScrollArea, QProgressBar, QDialog, QTextEdit, QTabWidget,
                             QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
                             QSpinBox, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont


class AzureControlPanel(QWidget):
    """Azure CLI control panel with buttons and configuration options"""

    # Signal to send commands to terminal
    command_requested = pyqtSignal(str)
    # Signal to request progress monitoring
    progress_command_requested = pyqtSignal(str, object)  # command, progress_dialog
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.load_azure_regions()
        self.load_vm_sizes()
        self.load_vm_images()
        # Initialize empty subscription dropdown
        self.subscription_combo.clear()
        self.subscription_combo.addItem("点击'刷新订阅'按钮加载订阅信息", "")

        # Initialize region loading state
        self._waiting_for_regions = False
        self._region_output_buffer = ""
        
        # Initialize quota detection state
        self._waiting_for_quota = False
        self._quota_output_buffer = ""
        self._quota_checking_regions = []
        self._current_quota_region = ""
        self._region_quotas = {}  # Store quota information for each region
        
    def init_ui(self):
        """Initialize the user interface"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(10)
        
        # Title
        title_label = QLabel("Azure CLI 控制面板")
        # Use default PyQt style with minimal formatting
        title_label.setAlignment(Qt.AlignCenter)
        font = title_label.font()
        font.setBold(True)
        font.setPointSize(14)
        title_label.setFont(font)
        main_layout.addWidget(title_label)
        
        # Create scroll area for content
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)
        
        # Content widget
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(10)
        
        # Add sections
        content_layout.addWidget(self.create_auth_section())
        content_layout.addWidget(self.create_config_section())
        content_layout.addWidget(self.create_resource_section())
        content_layout.addWidget(self.create_query_section())
        
        # Add stretch to push content to top
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        
    def create_auth_section(self):
        """Create Azure authentication section"""
        group = QGroupBox("Azure 认证")
        # Set Microsoft YaHei UI font for group box
        group_font = QFont("Microsoft YaHei UI", 9)
        group_font.setBold(True)
        group.setFont(group_font)

        layout = QVBoxLayout(group)
        layout.setSpacing(8)

        # Login button
        login_btn = self.create_command_button(
            "Azure 登录",
            "az login --use-device-code"
        )
        layout.addWidget(login_btn)

        # Clear account button
        clear_btn = QPushButton("清除账户")
        clear_btn.clicked.connect(self.clear_account)
        layout.addWidget(clear_btn)

        # Account info button
        info_btn = self.create_command_button(
            "账户信息",
            "az account show"
        )
        layout.addWidget(info_btn)

        # Auto refresh button
        refresh_btn = self.create_command_button(
            "刷新账户状态",
            "az account show"
        )
        layout.addWidget(refresh_btn)

        # Register compute provider button
        register_compute_btn = self.create_command_button(
            "注册Compute",
            "az provider register --namespace Microsoft.Network"
        )
        layout.addWidget(register_compute_btn)

        # Check registration progress button
        check_progress_btn = self.create_command_button(
            "检查注册进度",
            'az provider show --namespace Microsoft.Network --query "registrationState"'
        )
        layout.addWidget(check_progress_btn)

        # User details button
        user_details_btn = self.create_command_button(
            "用户详细信息",
            'az account show --query "{subscriptionId:id, subscriptionName:name, tenantId:tenantId, user:user, state:state, isDefault:isDefault}"'
        )
        layout.addWidget(user_details_btn)

        # Subscription management section
        subscription_layout = QHBoxLayout()

        # Refresh subscriptions button
        refresh_sub_btn = QPushButton("刷新订阅")
        refresh_sub_btn.clicked.connect(self.refresh_subscriptions)
        subscription_layout.addWidget(refresh_sub_btn)

        # Subscription dropdown (for future use)
        self.subscription_combo = QComboBox()
        self.subscription_combo.setPlaceholderText("选择订阅...")
        self.subscription_combo.setMinimumWidth(200)
        # Set Microsoft YaHei UI font for combo box
        combo_font = QFont("Microsoft YaHei UI", 9)
        self.subscription_combo.setFont(combo_font)
        subscription_layout.addWidget(self.subscription_combo)

        layout.addLayout(subscription_layout)

        return group
        
    def create_config_section(self):
        """Create configuration input section"""
        group = QGroupBox("虚拟机配置")
        # Set Microsoft YaHei UI font for group box
        group_font = QFont("Microsoft YaHei UI", 9)
        group_font.setBold(True)
        group.setFont(group_font)

        layout = QFormLayout(group)
        layout.setSpacing(8)

        # Resource group name
        self.resource_group_input = QLineEdit()
        self.resource_group_input.setPlaceholderText("请输入资源组名称")
        self.resource_group_input.setFont(QFont("Microsoft YaHei UI", 9))
        layout.addRow("资源组名称:", self.resource_group_input)

        # Username
        self.username_input = QLineEdit("ubuntu")
        self.username_input.setPlaceholderText("虚拟机管理员用户名")
        self.username_input.setFont(QFont("Microsoft YaHei UI", 9))
        layout.addRow("用户名:", self.username_input)

        # Password
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)  # Hide password input
        self.password_input.setPlaceholderText("虚拟机管理员密码")
        self.password_input.setFont(QFont("Microsoft YaHei UI", 9))
        layout.addRow("密码:", self.password_input)

        # Region selection with quota check and refresh buttons
        region_layout = QHBoxLayout()
        self.region_combo = QComboBox()
        # Use default PyQt style - no custom styling
        region_layout.addWidget(self.region_combo)

        # Refresh regions button
        refresh_regions_btn = QPushButton("刷新区域")
        refresh_regions_btn.clicked.connect(self.refresh_azure_regions_silent)
        region_layout.addWidget(refresh_regions_btn)

        # Quota check button
        quota_btn = QPushButton("配额检测")
        quota_btn.clicked.connect(self.check_region_quota)
        region_layout.addWidget(quota_btn)

        layout.addRow("区域:", region_layout)

        # VM size selection
        self.vm_size_combo = QComboBox()
        # Use default PyQt style - no custom styling
        layout.addRow("虚拟机规格:", self.vm_size_combo)

        # Image selection
        self.image_combo = QComboBox()
        # Use default PyQt style - no custom styling
        layout.addRow("镜像:", self.image_combo)

        # Disk size input
        self.disk_size_input = QLineEdit("64")
        self.disk_size_input.setPlaceholderText("磁盘大小(GB)")
        # Use default PyQt style - no custom styling
        layout.addRow("磁盘大小(GB):", self.disk_size_input)
        
        # Auto-fill section
        # DNS name (editable)
        self.dns_input = QLineEdit()
        self.dns_input.setPlaceholderText("请输入DNS名称或点击自动填充")
        # Use default PyQt style - no custom styling

        # VM name (auto-filled)
        self.vm_name_input = QLineEdit()
        self.vm_name_input.setPlaceholderText("点击自动填充按钮生成")
        # Use default PyQt style - no custom styling
        self.vm_name_input.setReadOnly(True)

        # Auto-fill button
        auto_fill_btn = QPushButton("自动填充")
        # Use default PyQt style - no custom styling
        auto_fill_btn.clicked.connect(self.auto_fill_names)

        layout.addRow("DNS 名称:", self.dns_input)
        layout.addRow("虚拟机名称:", self.vm_name_input)
        layout.addRow("", auto_fill_btn)
        
        # Connect resource group changes to auto-fill
        self.resource_group_input.textChanged.connect(self.auto_fill_names)
        
        return group
        
    def create_resource_section(self):
        """Create resource management section"""
        group = QGroupBox("资源管理")
        # Use default PyQt style - no custom styling

        layout = QVBoxLayout(group)
        layout.setSpacing(8)

        # Create resource group button
        create_rg_btn = self.create_dynamic_command_button(
            "创建资源组",
            lambda: f"az group create -n {self.resource_group_input.text()} -l {self.get_selected_region()}"
        )
        layout.addWidget(create_rg_btn)

        # Create VM button with progress
        create_vm_btn = QPushButton("创建虚拟机")
        create_vm_btn.clicked.connect(self.create_vm_with_progress)
        layout.addWidget(create_vm_btn)

        # Open ports button
        open_ports_btn = self.create_dynamic_command_button(
            "开放端口",
            lambda: f"az vm open-port -n {self.vm_name_input.text()} -g {self.resource_group_input.text()} --port 0-65535"
        )
        layout.addWidget(open_ports_btn)

        # Port management button
        port_mgmt_btn = QPushButton("出入站管理")
        port_mgmt_btn.clicked.connect(self.open_port_management)
        layout.addWidget(port_mgmt_btn)

        return group
        
    def create_query_section(self):
        """Create query and management section"""
        group = QGroupBox("查询与管理")
        # Use default PyQt style - no custom styling

        layout = QVBoxLayout(group)
        layout.setSpacing(8)

        # List resource groups
        list_rg_btn = self.create_command_button(
            "列出资源组",
            "az group list --output table"
        )
        layout.addWidget(list_rg_btn)

        # Delete resource group
        delete_rg_btn = self.create_dynamic_command_button(
            "删除资源组",
            lambda: f"az group delete --name {self.resource_group_input.text()} --yes --no-wait"
        )
        layout.addWidget(delete_rg_btn)

        # Query public IP
        query_ip_btn = self.create_dynamic_command_button(
            "查询公网IP",
            lambda: f"az network public-ip show --resource-group {self.resource_group_input.text()} --name {self.vm_name_input.text()}PublicIP --query ipAddress --output tsv"
        )
        layout.addWidget(query_ip_btn)

        # List VMs
        list_vm_btn = self.create_dynamic_command_button(
            "列出虚拟机",
            lambda: f"az vm list -g {self.resource_group_input.text()} --output table"
        )
        layout.addWidget(list_vm_btn)

        # List public IPs
        list_ip_btn = self.create_dynamic_command_button(
            "查询IP类型",
            lambda: f"az network public-ip show --resource-group {self.resource_group_input.text()} --name {self.vm_name_input.text()}PublicIP --output json"
        )
        layout.addWidget(list_ip_btn)

        return group

    def create_command_button(self, text, command):
        """Create a button that executes a fixed command"""
        btn = QPushButton(text)
        # Use default PyQt style - no custom styling
        btn.clicked.connect(lambda: self.command_requested.emit(command))
        return btn

    def create_dynamic_command_button(self, text, command_func):
        """Create a button that executes a dynamic command"""
        btn = QPushButton(text)
        # Use default PyQt style - no custom styling
        btn.clicked.connect(lambda: self.command_requested.emit(command_func()))
        return btn

    def get_create_vm_command(self):
        """Generate the VM creation command"""
        rg = self.resource_group_input.text()
        vm_name = self.vm_name_input.text()
        username = self.username_input.text()
        password = self.password_input.text()
        vm_size = self.get_selected_vm_size()
        image = self.get_selected_image()
        disk_size = self.disk_size_input.text() or "64"  # Default to 64GB if empty
        dns_name = self.dns_input.text()

        # Build command using password authentication only
        command = (f"az vm create "
                  f"--resource-group {rg} "
                  f"--name {vm_name} "
                  f"--image {image} "
                  f"--admin-username {username} "
                  f"--admin-password {password} "
                  f"--size {vm_size} "
                  f"--os-disk-size-gb {disk_size} "
                  f"--public-ip-address-dns-name {dns_name} "
                  f"--public-ip-address-allocation dynamic "
                  f"--public-ip-sku Basic")

        return command

    def auto_fill_names(self):
        """Auto-fill DNS and VM names based on resource group"""
        rg_name = self.resource_group_input.text()
        if rg_name:
            self.dns_input.setText(rg_name)
            self.vm_name_input.setText(rg_name)

    def get_selected_region(self):
        """Get the currently selected region"""
        return self.region_combo.currentData() or "southeastasia"

    def get_selected_vm_size(self):
        """Get the currently selected VM size"""
        return self.vm_size_combo.currentData() or "Standard_B1s"

    def get_selected_image(self):
        """Get the currently selected image"""
        return self.image_combo.currentData() or "Ubuntu2204"

    def load_azure_regions(self):
        """Load Azure regions into combo box - initially load from Azure CLI"""
        # Clear existing regions
        self.region_combo.clear()

        # Add loading message
        self.region_combo.addItem("正在加载区域列表...", "")

        # Try to load regions from Azure CLI
        self.refresh_azure_regions()

    def refresh_azure_regions(self):
        """Refresh Azure regions from Azure CLI"""
        # Mark that we're waiting for region data
        self._waiting_for_regions = True

        # Clear any previous buffer
        self._region_output_buffer = ""

        # Execute command to list all Azure regions
        command = 'az account list-locations --query "[].{name:name, displayName:displayName}" --output json'
        self.command_requested.emit(command)

    def refresh_azure_regions_silent(self):
        """Refresh Azure regions silently without showing command output"""
        import subprocess
        import json

        try:
            # Execute command silently
            command = 'az account list-locations --query "[].{name:name, displayName:displayName}" --output json'
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)

            if result.returncode == 0 and result.stdout.strip():
                # Parse and populate regions directly
                regions = json.loads(result.stdout.strip())
                self.populate_azure_regions(regions)
            else:
                # If command fails, load fallback regions
                self.load_fallback_regions()

        except (subprocess.TimeoutExpired, json.JSONDecodeError, Exception):
            # If any error occurs, load fallback regions
            self.load_fallback_regions()

    def populate_azure_regions(self, regions_data):
        """Populate region dropdown with real Azure region data"""
        import json

        # Clear existing items
        self.region_combo.clear()

        # Parse JSON data if it's a string
        if isinstance(regions_data, str):
            try:
                regions = json.loads(regions_data)
            except json.JSONDecodeError:
                self.region_combo.addItem("解析区域数据失败", "")
                self.load_fallback_regions()
                return
        else:
            regions = regions_data

        # Check if we have valid region data
        if not regions or not isinstance(regions, list):
            self.region_combo.addItem("未找到区域信息", "")
            self.load_fallback_regions()
            return

        # Filter out unwanted regions
        filtered_regions = []
        
        for region in regions:
            region_name = region.get('name', '').lower()
            display_name = region.get('displayName', '').lower()
            
            # Check if region should be excluded
            should_exclude = False
            
            # Exclude regions containing 'asia' but keep specific ones like 'southeastasia'
            if 'asia' in region_name or 'asia' in display_name:
                # Keep only Southeast Asia, exclude all other Asia regions
                if 'southeast' not in region_name and 'southeast' not in display_name:
                    should_exclude = True
            
            # Exclude Taiwan regions
            if 'taiwan' in region_name or 'taiwan' in display_name:
                should_exclude = True
            
            # Exclude Europe regions
            if 'europe' in region_name or 'europe' in display_name:
                should_exclude = True
            
            # Exclude stage/preview/test environments
            test_keywords = ['stage', 'euap', 'stg', 'canary', 'preview', 'test', 'dev']
            for keyword in test_keywords:
                if keyword in region_name or keyword in display_name:
                    should_exclude = True
                    break
            
            if not should_exclude:
                filtered_regions.append(region)
        
        # Sort filtered regions by display name for better user experience
        filtered_regions.sort(key=lambda x: x.get('displayName', ''))

        # Store original regions data for quota updates
        self._original_regions = filtered_regions

        # Populate dropdown with region data (with quota info if available)
        for region in filtered_regions:
            display_name = region.get('displayName', region.get('name', ''))
            region_name = region.get('name', '')
            if display_name and region_name:
                # Add quota information if available
                if hasattr(self, '_region_quotas') and region_name in self._region_quotas:
                    quota_info = self._region_quotas[region_name]
                    if quota_info == "Error":
                        display_text = f"{display_name} (❌ Error)"
                    elif quota_info == "Unknown":
                        display_text = f"{display_name} (❓ Unknown)"
                    else:
                        display_text = f"{display_name} (Limit: {quota_info})"
                else:
                    display_text = display_name
                
                self.region_combo.addItem(display_text, region_name)

        # Set default to Southeast Asia if available, otherwise first item
        southeast_asia_index = -1
        for i in range(self.region_combo.count()):
            if self.region_combo.itemData(i) == "southeastasia":
                southeast_asia_index = i
                break

        if southeast_asia_index >= 0:
            self.region_combo.setCurrentIndex(southeast_asia_index)
        elif self.region_combo.count() > 0:
            self.region_combo.setCurrentIndex(0)

    def load_fallback_regions(self):
        """Load fallback regions if Azure CLI fails"""
        fallback_regions = [
            ("Southeast Asia", "southeastasia"),
            ("East US", "eastus"),
            ("East US 2", "eastus2"),
            ("West US", "westus"),
            ("West US 2", "westus2"),
            ("Central US", "centralus"),
            ("Japan East", "japaneast"),
            ("Australia East", "australiaeast"),
            ("UK South", "uksouth"),
            ("Canada Central", "canadacentral"),
            ("Brazil South", "brazilsouth"),
            ("Korea Central", "koreacentral"),
            ("South India", "southindia"),
            ("Central India", "centralindia"),
        ]

        # Convert to the same format as Azure regions for consistency
        self._original_regions = []
        for display_name, region_code in fallback_regions:
            self._original_regions.append({
                'displayName': display_name,
                'name': region_code
            })

        for display_name, value in fallback_regions:
            # Add quota information if available
            if hasattr(self, '_region_quotas') and value in self._region_quotas:
                quota_info = self._region_quotas[value]
                if quota_info == "Error":
                    display_text = f"{display_name} (❌ Error)"
                elif quota_info == "Unknown":
                    display_text = f"{display_name} (❓ Unknown)"
                else:
                    display_text = f"{display_name} (Limit: {quota_info})"
            else:
                display_text = display_name
            
            self.region_combo.addItem(display_text, value)

        # Set default to Southeast Asia
        self.region_combo.setCurrentText("Southeast Asia")

    def load_vm_sizes(self):
        """Load VM sizes into combo box"""
        vm_sizes = [
            # Standard B1s
            ("Standard_B1s (1 vCPU, 1 GiB)", "Standard_B1s"),

            # Standard B2pts v2 - ARM-based
            ("Standard_B2pts_v2 (2 vCPUs, 1 GiB)", "Standard_B2pts_v2"),

            # Standard B2ats v2 - AMD-based
            ("Standard_B2ats_v2 (2 vCPUs, 1 GiB)", "Standard_B2ats_v2"),



        ]

        for display_name, value in vm_sizes:
            self.vm_size_combo.addItem(display_name, value)

        # Set default to B1s
        self.vm_size_combo.setCurrentText("Standard_B1s (1 vCPU, 1 GiB)")

    def load_vm_images(self):
        """Load VM images into combo box"""
        images = [
            ("Ubuntu 22.04 长期支持版", "Ubuntu2204"),
            ("Ubuntu 24.04 长期支持版", "Ubuntu2404"),
            ("Ubuntu 24.04 Pro", "Ubuntu2404Pro"),
            ("CentOS 8.5 Gen2", "CentOS85Gen2"),
            ("Debian 11", "Debian11"),
            ("OpenSUSE Leap 15.4 Gen2", "OpenSuseLeap154Gen2"),
            ("红帽企业版 8 LVM Gen2", "RHELRaw8LVMGen2"),
            ("SUSE Linux Enterprise Server 15 SP5", "SuseSles15SP5"),
            ("Flatcar Linux Free Gen2", "FlatcarLinuxFreeGen2"),
            ("Windows Server 2022", "Win2022Datacenter"),
            ("Windows Server 2022 Azure Edition Core", "Win2022AzureEditionCore"),
            ("Windows Server 2019", "Win2019Datacenter"),
            ("Windows Server 2016", "Win2016Datacenter"),
            ("Windows Server 2012 R2", "Win2012R2Datacenter"),
            ("Windows Server 2012", "Win2012Datacenter"),
        ]

        for display_name, value in images:
            self.image_combo.addItem(display_name, value)

        # Set default to Ubuntu 22.04
        self.image_combo.setCurrentText("Ubuntu 22.04 长期支持版")

    def check_region_quota(self):
        """Check quota for the currently selected region"""
        # Get the currently selected region
        current_region = self.get_selected_region()

        if not current_region:
            self.show_error("请先选择一个区域进行配额检测")
            return

        # Initialize quota checking state for single region
        self._quota_checking_regions = [current_region]
        self._region_quotas = {}
        self._waiting_for_quota = True
        self._quota_output_buffer = ""

        # Start checking the selected region
        self.check_next_region_quota()
    
    def check_next_region_quota(self):
        """Check quota for the next region in the queue"""
        if not self._quota_checking_regions:
            # All regions checked, update the dropdown
            self.update_region_dropdown_with_quotas()
            self._waiting_for_quota = False
            return
        
        # Get the next region to check
        self._current_quota_region = self._quota_checking_regions.pop(0)
        self._quota_output_buffer = ""
        
        # Execute quota check command for this region
        command = f'az network list-usages --location {self._current_quota_region} --query "[?name.localizedValue == \'Public IPv4 Addresses - Basic\']" --output table'
        self.command_requested.emit(command)
    
    def update_region_dropdown_with_quotas(self):
        """Update the region dropdown to include quota information for checked region"""
        # Update only the current region's display text with quota info
        self.update_current_region_quota_display()

    def update_current_region_quota_display(self):
        """Update the display text of the current region with quota information"""
        current_index = self.region_combo.currentIndex()
        if current_index < 0:
            return

        current_region_code = self.region_combo.itemData(current_index)
        if not current_region_code:
            return

        # Get the original display name (without quota info)
        current_text = self.region_combo.itemText(current_index)
        # Remove any existing quota information
        if " (Limit:" in current_text or " (❌" in current_text or " (❓" in current_text:
            # Extract the original display name
            original_name = current_text.split(" (")[0]
        else:
            original_name = current_text

        # Add quota information if available
        if current_region_code in self._region_quotas:
            quota_info = self._region_quotas[current_region_code]
            if quota_info == "Error":
                new_text = f"{original_name} (❌ Error)"
            elif quota_info == "Unknown":
                new_text = f"{original_name} (❓ Unknown)"
            else:
                new_text = f"{original_name} (Limit: {quota_info})"
        else:
            new_text = f"{original_name} (Limit: ?)"

        # Update the item text
        self.region_combo.setItemText(current_index, new_text)

    def populate_regions_with_quota_data(self):
        """Repopulate regions with quota information included"""
        # This method will be called to refresh the dropdown with quota data
        # We need to store the original region data first
        if not hasattr(self, '_original_regions'):
            # If we don't have original data, reload from Azure
            self.refresh_azure_regions()
            return
        
        # Save current selection
        current_region = self.get_selected_region()
        
        # Clear the combo box
        self.region_combo.clear()
        
        # Repopulate with quota information
        for region in self._original_regions:
            display_name = region.get('displayName', region.get('name', ''))
            region_code = region.get('name', '')
            
            if display_name and region_code:
                # Add quota information if available
                if region_code in self._region_quotas:
                    quota_info = self._region_quotas[region_code]
                    if quota_info == "Error":
                        display_text = f"{display_name} (❌ Error)"
                    elif quota_info == "Unknown":
                        display_text = f"{display_name} (❓ Unknown)"
                    else:
                        display_text = f"{display_name} (Limit: {quota_info})"
                else:
                    display_text = f"{display_name} (Limit: ?)"
                
                self.region_combo.addItem(display_text, region_code)
        
        # Restore previous selection
        for i in range(self.region_combo.count()):
            if self.region_combo.itemData(i) == current_region:
                self.region_combo.setCurrentIndex(i)
                break

    def create_vm_with_progress(self):
        """Create VM with detailed progress tracking"""
        # Validate inputs first
        if not self.resource_group_input.text().strip():
            self.show_error("请输入资源组名称")
            return
        if not self.vm_name_input.text().strip():
            self.show_error("请输入虚拟机名称")
            return
        if not self.username_input.text().strip():
            self.show_error("请输入用户名")
            return
        if not self.password_input.text().strip():
            self.show_error("请输入密码")
            return

        # Create and show progress dialog
        self.progress_dialog = VMCreationProgressDialog(self)
        self.progress_dialog.show()

        # Initialize progress tracking
        self.progress_step = 0
        self.progress_timer = None

        # Start the VM creation process
        self.start_vm_creation()

    def start_vm_creation(self):
        """Start the VM creation process with progress monitoring"""
        # Get the VM creation command
        command = self.get_create_vm_command()

        # Add verbose flag for detailed output
        command += " --verbose"

        # Update progress to first step
        self.progress_dialog.update_progress(0, f"执行命令: {command}")

        # Use the progress command signal
        self.progress_command_requested.emit(command, self.progress_dialog)

        # Start a timer to simulate progress updates based on typical Azure VM creation time
        self.progress_timer = QTimer()
        self.progress_timer.timeout.connect(self.update_vm_creation_progress)
        self.progress_step = 0
        self.progress_timer.start(3000)  # Update every 3 seconds

    def update_vm_creation_progress(self):
        """Update VM creation progress (simulated fallback)"""
        self.progress_step += 1

        # This is a fallback timer-based progress update
        # The real progress should come from parsing Azure CLI output
        if self.progress_step < len(self.progress_dialog.steps) - 2:  # Leave last 2 steps for real completion
            self.progress_dialog.update_progress(self.progress_step)
        else:
            # Don't auto-complete, wait for real command completion
            pass

    def show_error(self, message):
        """Show error message"""
        # For now, just emit a command to show the error
        self.command_requested.emit(f"echo 错误: {message}")

    def stop_progress_timer(self):
        """Stop the progress timer"""
        if hasattr(self, 'progress_timer') and self.progress_timer:
            self.progress_timer.stop()
            self.progress_timer = None

    def clear_account(self):
        """Clear Azure account and reset subscription dropdown"""
        # Execute the clear command
        self.command_requested.emit("az account clear")

        # Clear the subscription dropdown immediately
        self.subscription_combo.clear()
        self.subscription_combo.addItem("账户已清除", "")

        # Reset any waiting flags
        self._waiting_for_subscriptions = False
        self._waiting_for_regions = False
        if hasattr(self, '_subscription_output_buffer'):
            self._subscription_output_buffer = ""
        if hasattr(self, '_region_output_buffer'):
            self._region_output_buffer = ""

    def refresh_subscriptions(self):
        """Refresh and load Azure subscriptions"""
        # Mark that we're waiting for subscription data
        self._waiting_for_subscriptions = True

        # Clear any previous buffer
        self._subscription_output_buffer = ""

        # Execute command to list all subscriptions with detailed information
        command = 'az account list --query "[].{name:name, id:id, state:state, isDefault:isDefault, tenantId:tenantId}" --output json'
        self.command_requested.emit(command)

    def populate_subscriptions(self, subscriptions_data):
        """Populate subscription dropdown with real Azure subscription data"""
        import json

        # Clear existing items
        self.subscription_combo.clear()

        # Parse JSON data if it's a string
        if isinstance(subscriptions_data, str):
            try:
                subscriptions = json.loads(subscriptions_data)
            except json.JSONDecodeError:
                self.subscription_combo.addItem("解析订阅数据失败", "")
                return
        else:
            subscriptions = subscriptions_data

        # Check if we have valid subscription data
        if not subscriptions or not isinstance(subscriptions, list):
            self.subscription_combo.addItem("未找到订阅信息", "")
            return

        # Populate dropdown with subscription data
        for sub in subscriptions:
            display_text = f"{sub['name']} ({sub['state']})"
            if sub.get('isDefault', False):
                display_text += " [默认]"
            self.subscription_combo.addItem(display_text, sub['id'])

        # Set default selection
        if subscriptions:
            for i, sub in enumerate(subscriptions):
                if sub.get('isDefault', False):
                    self.subscription_combo.setCurrentIndex(i)
                    break

    def populate_empty_subscriptions(self):
        """Populate subscription dropdown when no subscriptions are found"""
        # Clear existing items
        self.subscription_combo.clear()

        # Add message indicating no subscriptions
        self.subscription_combo.addItem("未找到订阅 - 请先登录Azure账户", "")

    def handle_terminal_output(self, output):
        """Handle terminal output to detect subscription and region data"""
        # Check if we're waiting for subscription data
        if hasattr(self, '_waiting_for_subscriptions') and self._waiting_for_subscriptions:
            # Accumulate output for JSON parsing
            if not hasattr(self, '_subscription_output_buffer'):
                self._subscription_output_buffer = ""

            self._subscription_output_buffer += output

            # Look for complete JSON array
            if self._subscription_output_buffer.strip().startswith('[') and self._subscription_output_buffer.strip().endswith(']'):
                try:
                    import json
                    # Try to parse the accumulated output as JSON
                    subscriptions = json.loads(self._subscription_output_buffer.strip())
                    if isinstance(subscriptions, list):
                        if len(subscriptions) > 0:
                            # Check if it looks like subscription data
                            first_item = subscriptions[0]
                            if 'name' in first_item and 'id' in first_item and 'state' in first_item:
                                self.populate_subscriptions(subscriptions)
                                self._waiting_for_subscriptions = False
                                self._subscription_output_buffer = ""
                        else:
                            # Empty array - no subscriptions (account cleared)
                            self.populate_empty_subscriptions()
                            self._waiting_for_subscriptions = False
                            self._subscription_output_buffer = ""
                except json.JSONDecodeError:
                    # If JSON is incomplete, continue accumulating
                    pass

        # Check if we're waiting for region data
        if hasattr(self, '_waiting_for_regions') and self._waiting_for_regions:
            # Accumulate output for JSON parsing
            if not hasattr(self, '_region_output_buffer'):
                self._region_output_buffer = ""

            self._region_output_buffer += output

            # Look for complete JSON array
            if self._region_output_buffer.strip().startswith('[') and self._region_output_buffer.strip().endswith(']'):
                try:
                    import json
                    # Try to parse the accumulated output as JSON
                    regions = json.loads(self._region_output_buffer.strip())
                    if isinstance(regions, list):
                        if len(regions) > 0:
                            # Check if it looks like region data
                            first_item = regions[0]
                            if 'name' in first_item and 'displayName' in first_item:
                                self.populate_azure_regions(regions)
                                self._waiting_for_regions = False
                                self._region_output_buffer = ""
                        else:
                            # Empty array - no regions found, use fallback
                            self.load_fallback_regions()
                            self._waiting_for_regions = False
                            self._region_output_buffer = ""
                except json.JSONDecodeError:
                    # If JSON is incomplete, continue accumulating
                    pass
        
        # Check if we're waiting for quota data
        if hasattr(self, '_waiting_for_quota') and self._waiting_for_quota:
            # Accumulate output for quota parsing
            self._quota_output_buffer += output
            
            # Check for errors first
            if 'ERROR:' in self._quota_output_buffer or 'NoRegisteredProviderFound' in self._quota_output_buffer:
                # This region has an error, mark as error and move to next
                self._region_quotas[self._current_quota_region] = "Error"
                self._quota_output_buffer = ""
                self.check_next_region_quota()
                return
            
            # Look for quota table output pattern
            # The quota command outputs a table with headers: CurrentValue, Limit, Unit, LocalName
            lines = self._quota_output_buffer.strip().split('\n')
            
            # Check if we have received the complete table
            if len(lines) >= 3:  # Header line, separator line, data line(s)
                # Look for the header line
                header_found = False
                data_found = False
                
                for i, line in enumerate(lines):
                    if 'CurrentValue' in line and 'Limit' in line and 'LocalName' in line:
                        header_found = True
                        continue
                    
                    if header_found and '---' in line:
                        continue
                        
                    if header_found and 'Public IPv4 Addresses - Basic' in line:
                        # Parse the data line
                        parts = line.split()
                        if len(parts) >= 2:
                            try:
                                current_value = parts[0]
                                limit_value = parts[1]
                                # Store the quota information
                                self._region_quotas[self._current_quota_region] = limit_value
                                data_found = True
                                break
                            except (ValueError, IndexError):
                                # If parsing fails, mark as unknown
                                self._region_quotas[self._current_quota_region] = "Unknown"
                                data_found = True
                                break
                
                # If we found data or reached a reasonable timeout, move to next region
                if data_found or len(self._quota_output_buffer) > 2000:  # Increased timeout for error messages
                    if not data_found:
                        # Check if this looks like an error message
                        if any(error_keyword in self._quota_output_buffer.lower() 
                               for error_keyword in ['error', 'failed', 'not found', 'invalid']):
                            self._region_quotas[self._current_quota_region] = "Error"
                        else:
                            # Mark as unknown if no valid data found
                            self._region_quotas[self._current_quota_region] = "Unknown"
                    
                    # Clear the buffer and check next region
                    self._quota_output_buffer = ""
                    self.check_next_region_quota()

    def open_port_management(self):
        """Open port management dialog"""
        if not self.resource_group_input.text().strip():
            self.show_error("请先输入资源组名称")
            return
        if not self.vm_name_input.text().strip():
            self.show_error("请先输入虚拟机名称")
            return

        dialog = PortManagementDialog(self, self.resource_group_input.text(), self.vm_name_input.text())
        dialog.command_requested.connect(self.command_requested.emit)
        dialog.exec_()


class VMCreationProgressDialog(QDialog):
    """Progress dialog for VM creation with detailed steps"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.current_step = 0
        self.steps = [
            "正在验证参数和配置...",
            "正在检查资源组状态...",
            "正在创建或验证虚拟网络...",
            "正在创建网络安全组...",
            "正在分配公共IP地址...",
            "正在创建网络接口...",
            "正在准备虚拟机配置...",
            "正在创建虚拟机实例...",
            "正在配置操作系统磁盘...",
            "正在下载和部署镜像...",
            "正在配置网络和安全设置...",
            "正在启动虚拟机...",
            "正在进行最终配置检查...",
            "虚拟机创建完成！"
        ]

    def init_ui(self):
        """Initialize the progress dialog UI"""
        self.setWindowTitle("Azure 虚拟机创建进度")
        self.setModal(True)
        self.setFixedSize(500, 400)

        layout = QVBoxLayout(self)

        # Title
        title_label = QLabel("正在创建 Azure 虚拟机...")
        title_label.setAlignment(Qt.AlignCenter)
        font = title_label.font()
        font.setBold(True)
        font.setPointSize(14)
        title_label.setFont(font)
        layout.addWidget(title_label)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)

        # Current step label
        self.step_label = QLabel("准备开始...")
        self.step_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.step_label)

        # Detailed output
        output_label = QLabel("详细输出:")
        layout.addWidget(output_label)

        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        self.output_text.setMaximumHeight(200)
        layout.addWidget(self.output_text)

        # Cancel button
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.close_dialog)
        layout.addWidget(self.cancel_button)

    def update_progress(self, step_index, message=""):
        """Update the progress dialog"""
        if step_index < len(self.steps):
            self.current_step = step_index
            progress = int((step_index / (len(self.steps) - 1)) * 100)
            self.progress_bar.setValue(progress)
            self.step_label.setText(self.steps[step_index])

            if message:
                self.output_text.append(message)
                # Auto-scroll to bottom
                scrollbar = self.output_text.verticalScrollBar()
                scrollbar.setValue(scrollbar.maximum())

    def add_output(self, text):
        """Add text to the output area"""
        self.output_text.append(text)
        # Auto-scroll to bottom
        scrollbar = self.output_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def set_completed(self):
        """Mark the creation as completed"""
        self.progress_bar.setValue(100)
        self.step_label.setText("虚拟机创建完成！")
        self.cancel_button.setText("关闭")

    def set_error(self, error_message):
        """Mark the creation as failed"""
        self.step_label.setText("创建失败！")
        self.add_output(f"错误: {error_message}")
        self.cancel_button.setText("关闭")

    def close_dialog(self):
        """Close the dialog safely"""
        # Just hide the dialog instead of rejecting it
        self.hide()

    def closeEvent(self, event):
        """Handle window close event"""
        # Override the close event to just hide the dialog
        self.hide()
        event.ignore()  # Ignore the close event to prevent application exit


class PortManagementDialog(QDialog):
    """Port management dialog with inbound, outbound, and open all ports tabs"""

    command_requested = pyqtSignal(str)

    def __init__(self, parent=None, resource_group="", vm_name=""):
        super().__init__(parent)
        self.resource_group = resource_group
        self.vm_name = vm_name
        self.nsg_name = f"{vm_name}NSG"  # Default NSG name pattern
        self.init_ui()

    def init_ui(self):
        """Initialize the port management dialog UI"""
        self.setWindowTitle("出入站端口管理")
        self.setModal(True)
        self.setFixedSize(800, 600)

        # Set dialog background style
        self.setStyleSheet("""
            QDialog {
                background-color: #2C2C2C;
                color: #FFFFFF;
            }
            QLabel {
                color: #FFFFFF;
            }
            QLineEdit {
                background-color: #404040;
                color: #FFFFFF;
                border: 1px solid #606060;
                padding: 5px;
                border-radius: 3px;
            }
            QSpinBox {
                background-color: #404040;
                color: #FFFFFF;
                border: 1px solid #606060;
                padding: 5px;
                border-radius: 3px;
            }
            QComboBox {
                background-color: #404040;
                color: #FFFFFF;
                border: 1px solid #606060;
                padding: 5px;
                border-radius: 3px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #FFFFFF;
            }
            QPushButton {
                background-color: #0078D4;
                color: #FFFFFF;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005A9E;
            }
            QPushButton:pressed {
                background-color: #004578;
            }
            QCheckBox {
                color: #FFFFFF;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                background-color: #404040;
                border: 1px solid #606060;
            }
            QCheckBox::indicator:checked {
                background-color: #0078D4;
                border: 1px solid #0078D4;
            }
        """)

        layout = QVBoxLayout(self)

        # Title
        title_label = QLabel(f"虚拟机: {self.vm_name} - 出入站端口管理")
        title_label.setAlignment(Qt.AlignCenter)
        font = title_label.font()
        font.setBold(True)
        font.setPointSize(14)
        title_label.setFont(font)
        layout.addWidget(title_label)

        # Create tab widget
        self.tab_widget = QTabWidget()
        # Set tab widget style for better visibility
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #606060;
                background-color: #2C2C2C;
                color: #FFFFFF;
            }
            QTabBar::tab {
                background-color: #404040;
                color: #FFFFFF;
                padding: 10px 20px;
                margin-right: 3px;
                border: 2px solid #606060;
                border-bottom: none;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                font-size: 12px;
                min-width: 80px;
            }
            QTabBar::tab:selected {
                background-color: #0078D4;
                color: #FFFFFF;
                border: 2px solid #0078D4;
                border-bottom: 2px solid #2C2C2C;
            }
            QTabBar::tab:hover {
                background-color: #005A9E;
                color: #FFFFFF;
            }
            QTabBar::tab:!selected {
                margin-top: 2px;
            }
        """)
        layout.addWidget(self.tab_widget)

        # Create tabs
        self.inbound_tab = self.create_inbound_tab()
        self.outbound_tab = self.create_outbound_tab()
        self.open_all_tab = self.create_open_all_tab()

        self.tab_widget.addTab(self.inbound_tab, "入站配置")
        self.tab_widget.addTab(self.outbound_tab, "出站配置")
        self.tab_widget.addTab(self.open_all_tab, "开放所有端口")

        # Close button
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)

        # Load existing rules when dialog opens
        self.load_existing_rules()

    def create_inbound_tab(self):
        """Create inbound rules configuration tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Refresh button
        refresh_btn = QPushButton("刷新入站规则")
        refresh_btn.clicked.connect(self.refresh_inbound_rules)
        layout.addWidget(refresh_btn)

        # Inbound rules table
        self.inbound_table = QTableWidget()
        self.inbound_table.setColumnCount(7)
        self.inbound_table.setHorizontalHeaderLabels([
            "规则名称", "优先级", "源", "端口", "协议", "操作", "删除"
        ])

        # Set table properties
        header = self.inbound_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.inbound_table.setAlternatingRowColors(True)
        self.inbound_table.setSelectionBehavior(QTableWidget.SelectRows)

        # Set table style for better visibility
        self.inbound_table.setStyleSheet("""
            QTableWidget {
                background-color: #2C2C2C;
                color: #FFFFFF;
                gridline-color: #404040;
                border: 1px solid #404040;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #404040;
            }
            QTableWidget::item:selected {
                background-color: #505050;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #FFFFFF;
                padding: 8px;
                border: 1px solid #606060;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.inbound_table)

        # Add new rule section
        add_group = QGroupBox("添加新入站规则")
        add_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #404040;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        add_layout = QFormLayout(add_group)

        self.inbound_name_input = QLineEdit()
        self.inbound_name_input.setPlaceholderText("规则名称")
        add_layout.addRow("规则名称:", self.inbound_name_input)

        self.inbound_priority_input = QSpinBox()
        self.inbound_priority_input.setRange(100, 4096)
        self.inbound_priority_input.setValue(1000)
        add_layout.addRow("优先级:", self.inbound_priority_input)

        self.inbound_source_input = QLineEdit("*")
        self.inbound_source_input.setPlaceholderText("源地址 (例如: *, 192.168.1.0/24)")
        add_layout.addRow("源地址:", self.inbound_source_input)

        self.inbound_port_input = QLineEdit()
        self.inbound_port_input.setPlaceholderText("端口 (例如: 80, 80-90, *)")
        add_layout.addRow("目标端口:", self.inbound_port_input)

        self.inbound_protocol_combo = QComboBox()
        self.inbound_protocol_combo.addItems(["Tcp", "Udp", "*"])
        add_layout.addRow("协议:", self.inbound_protocol_combo)

        self.inbound_action_combo = QComboBox()
        self.inbound_action_combo.addItems(["Allow", "Deny"])
        add_layout.addRow("操作:", self.inbound_action_combo)

        add_inbound_btn = QPushButton("添加入站规则")
        add_inbound_btn.clicked.connect(self.add_inbound_rule)
        add_layout.addRow("", add_inbound_btn)

        layout.addWidget(add_group)

        return widget

    def create_outbound_tab(self):
        """Create outbound rules configuration tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Refresh button
        refresh_btn = QPushButton("刷新出站规则")
        refresh_btn.clicked.connect(self.refresh_outbound_rules)
        layout.addWidget(refresh_btn)

        # Outbound rules table
        self.outbound_table = QTableWidget()
        self.outbound_table.setColumnCount(7)
        self.outbound_table.setHorizontalHeaderLabels([
            "规则名称", "优先级", "目标", "端口", "协议", "操作", "删除"
        ])

        # Set table properties
        header = self.outbound_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.outbound_table.setAlternatingRowColors(True)
        self.outbound_table.setSelectionBehavior(QTableWidget.SelectRows)

        # Set table style for better visibility
        self.outbound_table.setStyleSheet("""
            QTableWidget {
                background-color: #2C2C2C;
                color: #FFFFFF;
                gridline-color: #404040;
                border: 1px solid #404040;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #404040;
            }
            QTableWidget::item:selected {
                background-color: #505050;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #FFFFFF;
                padding: 8px;
                border: 1px solid #606060;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.outbound_table)

        # Add new rule section
        add_group = QGroupBox("添加新出站规则")
        add_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #404040;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        add_layout = QFormLayout(add_group)

        self.outbound_name_input = QLineEdit()
        self.outbound_name_input.setPlaceholderText("规则名称")
        add_layout.addRow("规则名称:", self.outbound_name_input)

        self.outbound_priority_input = QSpinBox()
        self.outbound_priority_input.setRange(100, 4096)
        self.outbound_priority_input.setValue(1000)
        add_layout.addRow("优先级:", self.outbound_priority_input)

        self.outbound_dest_input = QLineEdit("*")
        self.outbound_dest_input.setPlaceholderText("目标地址 (例如: *, 192.168.1.0/24)")
        add_layout.addRow("目标地址:", self.outbound_dest_input)

        self.outbound_port_input = QLineEdit()
        self.outbound_port_input.setPlaceholderText("端口 (例如: 80, 80-90, *)")
        add_layout.addRow("目标端口:", self.outbound_port_input)

        self.outbound_protocol_combo = QComboBox()
        self.outbound_protocol_combo.addItems(["Tcp", "Udp", "*"])
        add_layout.addRow("协议:", self.outbound_protocol_combo)

        self.outbound_action_combo = QComboBox()
        self.outbound_action_combo.addItems(["Allow", "Deny"])
        add_layout.addRow("操作:", self.outbound_action_combo)

        add_outbound_btn = QPushButton("添加出站规则")
        add_outbound_btn.clicked.connect(self.add_outbound_rule)
        add_layout.addRow("", add_outbound_btn)

        layout.addWidget(add_group)

        return widget

    def create_open_all_tab(self):
        """Create open all ports tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Warning message
        warning_label = QLabel("⚠️ 警告: 开放所有端口会带来安全风险，请谨慎使用！")
        warning_label.setStyleSheet("color: red; font-weight: bold; font-size: 14px;")
        warning_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(warning_label)

        # Description
        desc_label = QLabel("此功能将创建规则开放所有端口（0-65535）的入站和出站访问。")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)

        # Options
        options_group = QGroupBox("开放选项")
        options_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #404040;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        options_layout = QVBoxLayout(options_group)

        self.open_inbound_check = QCheckBox("开放所有入站端口 (0-65535)")
        self.open_inbound_check.setChecked(True)
        options_layout.addWidget(self.open_inbound_check)

        self.open_outbound_check = QCheckBox("开放所有出站端口 (0-65535)")
        self.open_outbound_check.setChecked(True)
        options_layout.addWidget(self.open_outbound_check)

        layout.addWidget(options_group)

        # Protocol selection
        protocol_group = QGroupBox("协议选择")
        protocol_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #404040;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        protocol_layout = QVBoxLayout(protocol_group)

        self.tcp_check = QCheckBox("TCP")
        self.tcp_check.setChecked(True)
        protocol_layout.addWidget(self.tcp_check)

        self.udp_check = QCheckBox("UDP")
        self.udp_check.setChecked(True)
        protocol_layout.addWidget(self.udp_check)

        layout.addWidget(protocol_group)

        # Execute button
        execute_btn = QPushButton("执行开放所有端口")
        execute_btn.setStyleSheet("background-color: #ff6b6b; color: white; font-weight: bold; padding: 10px;")
        execute_btn.clicked.connect(self.open_all_ports)
        layout.addWidget(execute_btn)

        # Add stretch to center content
        layout.addStretch()

        return widget

    def load_existing_rules(self):
        """Load existing NSG rules"""
        # Load inbound rules
        self.refresh_inbound_rules()
        # Load outbound rules
        self.refresh_outbound_rules()
        # Populate tables with sample data for demonstration
        self.populate_inbound_table([])
        self.populate_outbound_table([])

    def refresh_inbound_rules(self):
        """Refresh inbound rules table"""
        command = f'az network nsg rule list --resource-group {self.resource_group} --nsg-name {self.nsg_name} --query "[?direction==\'Inbound\']" --output json'
        self.command_requested.emit(command)

    def refresh_outbound_rules(self):
        """Refresh outbound rules table"""
        command = f'az network nsg rule list --resource-group {self.resource_group} --nsg-name {self.nsg_name} --query "[?direction==\'Outbound\']" --output json'
        self.command_requested.emit(command)

    def add_inbound_rule(self):
        """Add new inbound rule"""
        name = self.inbound_name_input.text().strip()
        if not name:
            QMessageBox.warning(self, "错误", "请输入规则名称")
            return

        priority = self.inbound_priority_input.value()
        source = self.inbound_source_input.text().strip()
        port = self.inbound_port_input.text().strip()
        protocol = self.inbound_protocol_combo.currentText()
        action = self.inbound_action_combo.currentText()

        if not port:
            QMessageBox.warning(self, "错误", "请输入目标端口")
            return

        command = (f'az network nsg rule create '
                  f'--resource-group {self.resource_group} '
                  f'--nsg-name {self.nsg_name} '
                  f'--name {name} '
                  f'--priority {priority} '
                  f'--source-address-prefixes {source} '
                  f'--destination-port-ranges {port} '
                  f'--protocol {protocol} '
                  f'--access {action} '
                  f'--direction Inbound')

        self.command_requested.emit(command)

        # Clear inputs
        self.inbound_name_input.clear()
        self.inbound_priority_input.setValue(1000)
        self.inbound_source_input.setText("*")
        self.inbound_port_input.clear()

    def add_outbound_rule(self):
        """Add new outbound rule"""
        name = self.outbound_name_input.text().strip()
        if not name:
            QMessageBox.warning(self, "错误", "请输入规则名称")
            return

        priority = self.outbound_priority_input.value()
        dest = self.outbound_dest_input.text().strip()
        port = self.outbound_port_input.text().strip()
        protocol = self.outbound_protocol_combo.currentText()
        action = self.outbound_action_combo.currentText()

        if not port:
            QMessageBox.warning(self, "错误", "请输入目标端口")
            return

        command = (f'az network nsg rule create '
                  f'--resource-group {self.resource_group} '
                  f'--nsg-name {self.nsg_name} '
                  f'--name {name} '
                  f'--priority {priority} '
                  f'--destination-address-prefixes {dest} '
                  f'--destination-port-ranges {port} '
                  f'--protocol {protocol} '
                  f'--access {action} '
                  f'--direction Outbound')

        self.command_requested.emit(command)

        # Clear inputs
        self.outbound_name_input.clear()
        self.outbound_priority_input.setValue(1000)
        self.outbound_dest_input.setText("*")
        self.outbound_port_input.clear()

    def open_all_ports(self):
        """Open all ports based on selected options"""
        reply = QMessageBox.question(self, "确认操作",
                                   "确定要开放所有端口吗？这可能会带来安全风险！",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        commands = []

        # Generate protocols list
        protocols = []
        if self.tcp_check.isChecked():
            protocols.append("Tcp")
        if self.udp_check.isChecked():
            protocols.append("Udp")

        if not protocols:
            QMessageBox.warning(self, "错误", "请至少选择一个协议")
            return

        # Create inbound rules
        if self.open_inbound_check.isChecked():
            for protocol in protocols:
                rule_name = f"OpenAll_Inbound_{protocol}"
                command = (f'az network nsg rule create '
                          f'--resource-group {self.resource_group} '
                          f'--nsg-name {self.nsg_name} '
                          f'--name {rule_name} '
                          f'--priority 100 '
                          f'--source-address-prefixes "*" '
                          f'--destination-port-ranges "0-65535" '
                          f'--protocol {protocol} '
                          f'--access Allow '
                          f'--direction Inbound')
                commands.append(command)

        # Create outbound rules
        if self.open_outbound_check.isChecked():
            for protocol in protocols:
                rule_name = f"OpenAll_Outbound_{protocol}"
                command = (f'az network nsg rule create '
                          f'--resource-group {self.resource_group} '
                          f'--nsg-name {self.nsg_name} '
                          f'--name {rule_name} '
                          f'--priority 100 '
                          f'--destination-address-prefixes "*" '
                          f'--destination-port-ranges "0-65535" '
                          f'--protocol {protocol} '
                          f'--access Allow '
                          f'--direction Outbound')
                commands.append(command)

        # Execute all commands
        for command in commands:
            self.command_requested.emit(command)

    def delete_rule(self, rule_name):
        """Delete a specific rule"""
        reply = QMessageBox.question(self, "确认删除",
                                   f"确定要删除规则 '{rule_name}' 吗？",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)

        if reply == QMessageBox.Yes:
            command = (f'az network nsg rule delete '
                      f'--resource-group {self.resource_group} '
                      f'--nsg-name {self.nsg_name} '
                      f'--name {rule_name}')
            self.command_requested.emit(command)

    def populate_inbound_table(self, rules_data):
        """Populate inbound rules table with data"""
        # This method would be called when we receive the JSON response
        # For now, we'll add a sample implementation
        self.inbound_table.setRowCount(0)  # Clear existing rows

        # Sample data structure - in real implementation, this would parse JSON
        # rules_data should be a list of rule dictionaries
        sample_rules = [
            {"name": "SSH", "priority": 1000, "source": "*", "port": "22", "protocol": "Tcp", "access": "Allow"},
            {"name": "HTTP", "priority": 1001, "source": "*", "port": "80", "protocol": "Tcp", "access": "Allow"},
        ]

        for i, rule in enumerate(sample_rules):
            self.inbound_table.insertRow(i)
            self.inbound_table.setItem(i, 0, QTableWidgetItem(rule["name"]))
            self.inbound_table.setItem(i, 1, QTableWidgetItem(str(rule["priority"])))
            self.inbound_table.setItem(i, 2, QTableWidgetItem(rule["source"]))
            self.inbound_table.setItem(i, 3, QTableWidgetItem(rule["port"]))
            self.inbound_table.setItem(i, 4, QTableWidgetItem(rule["protocol"]))
            self.inbound_table.setItem(i, 5, QTableWidgetItem(rule["access"]))

            # Add delete button
            delete_btn = QPushButton("删除")
            delete_btn.clicked.connect(lambda checked=False, name=rule["name"]: self.delete_rule(name))
            self.inbound_table.setCellWidget(i, 6, delete_btn)

    def populate_outbound_table(self, rules_data):
        """Populate outbound rules table with data"""
        # This method would be called when we receive the JSON response
        # For now, we'll add a sample implementation
        self.outbound_table.setRowCount(0)  # Clear existing rows

        # Sample data structure - in real implementation, this would parse JSON
        sample_rules = [
            {"name": "AllowInternetOutbound", "priority": 65001, "destination": "*", "port": "*", "protocol": "*", "access": "Allow"},
        ]

        for i, rule in enumerate(sample_rules):
            self.outbound_table.insertRow(i)
            self.outbound_table.setItem(i, 0, QTableWidgetItem(rule["name"]))
            self.outbound_table.setItem(i, 1, QTableWidgetItem(str(rule["priority"])))
            self.outbound_table.setItem(i, 2, QTableWidgetItem(rule["destination"]))
            self.outbound_table.setItem(i, 3, QTableWidgetItem(rule["port"]))
            self.outbound_table.setItem(i, 4, QTableWidgetItem(rule["protocol"]))
            self.outbound_table.setItem(i, 5, QTableWidgetItem(rule["access"]))

            # Add delete button
            delete_btn = QPushButton("删除")
            delete_btn.clicked.connect(lambda checked=False, name=rule["name"]: self.delete_rule(name))
            self.outbound_table.setCellWidget(i, 6, delete_btn)
