(['D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt\\azure_panel.py',
  'D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt\\command_executor.py',
  'D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt\\main.py',
  'D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt\\terminal_widget.py'],
 ['D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt',
  'D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt',
  'D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt',
  'D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt'],
 [],
 ['D:\\Python312\\Lib\\site-packages\\aspose\\__pyinstaller',
  'D:\\Python312\\Lib\\site-packages\\aspose\\email\\__pyinstaller',
  'D:\\Python312\\Lib\\site-packages\\aspose\\pyreflection\\__pyinstaller',
  'D:\\Python312\\Lib\\site-packages\\aspose\\pygc\\__pyinstaller',
  'D:\\Python312\\Lib\\site-packages\\aspose\\pycore\\__pyinstaller',
  'D:\\Python312\\Lib\\site-packages\\numpy\\_pyinstaller',
  'D:\\Python312\\Lib\\site-packages\\playwright\\_impl\\__pyinstaller',
  'D:\\Python312\\Lib\\site-packages\\pygame\\__pyinstaller',
  'D:\\Python312\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
  'D:\\Python312\\Lib\\site-packages\\_pyinstaller_hooks_contrib'],
 {},
 [],
 [],
 False,
 {},
 [],
 [],
 '3.12.1 (tags/v3.12.1:2305ca5, Dec  7 2023, 22:03:25) [MSC v.1937 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('azure_panel',
   'D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt\\azure_panel.py',
   'PYSOURCE'),
  ('command_executor',
   'D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt\\command_executor.py',
   'PYSOURCE'),
  ('main', 'D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt\\main.py', 'PYSOURCE'),
  ('terminal_widget',
   'D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt\\terminal_widget.py',
   'PYSOURCE')],
 [('_pyi_rth_utils',
   'D:\\Python312\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\Python312\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\Python312\\Lib\\zipimport.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('typing', 'D:\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('contextlib', 'D:\\Python312\\Lib\\contextlib.py', 'PYMODULE'),
  ('importlib.resources',
   'D:\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'D:\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'D:\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\Python312\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Python312\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\Python312\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'D:\\Python312\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'D:\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\Python312\\Lib\\dataclasses.py', 'PYMODULE'),
  ('copy', 'D:\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\Python312\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('struct', 'D:\\Python312\\Lib\\struct.py', 'PYMODULE'),
  ('threading', 'D:\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'D:\\Python312\\Lib\\_threading_local.py', 'PYMODULE'),
  ('string', 'D:\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('bisect', 'D:\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('shutil', 'D:\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('argparse', 'D:\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('gettext', 'D:\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'D:\\Python312\\Lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'D:\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('zipfile', 'D:\\Python312\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'D:\\Python312\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\Python312\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\Python312\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'D:\\Python312\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\Python312\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'D:\\Python312\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'D:\\Python312\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'D:\\Python312\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'D:\\Python312\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'D:\\Python312\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\Python312\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\Python312\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'D:\\Python312\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'D:\\Python312\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'D:\\Python312\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr', 'D:\\Python312\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'D:\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\Python312\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('datetime', 'D:\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\Python312\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_strptime', 'D:\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('socket', 'D:\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'D:\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('email', 'D:\\Python312\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\Python312\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\Python312\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('csv', 'D:\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'D:\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('inspect', 'D:\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\Python312\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib', 'D:\\Python312\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('pathlib', 'D:\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('command_executor',
   'D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt\\command_executor.py',
   'PYMODULE'),
  ('signal', 'D:\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('subprocess', 'D:\\Python312\\Lib\\subprocess.py', 'PYMODULE'),
  ('azure_panel',
   'D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt\\azure_panel.py',
   'PYMODULE'),
  ('terminal_widget',
   'D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt\\terminal_widget.py',
   'PYMODULE'),
  ('stringprep', 'D:\\Python312\\Lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\Python312\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('json', 'D:\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\Python312\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\Python312\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\Python312\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('PyQt5',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE')],
 [('python312.dll', 'D:\\Python312\\python312.dll', 'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_decimal.pyd', 'D:\\Python312\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\Python312\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\Python312\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Python312\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp312-win_amd64.pyd',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\Python312\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\Python312\\VCRUNTIME140_1.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'D:\\Python312\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('python3.dll', 'D:\\Python312\\python3.dll', 'BINARY'),
  ('ucrtbase.dll', 'D:\\Java\\jdk-********\\bin\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Java\\jdk-********\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'D:\\Chrome下载\\tg下载资源\\check\\az_gui_qt\\build\\azure_panel\\base_library.zip',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA')])
